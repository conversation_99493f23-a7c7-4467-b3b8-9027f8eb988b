---
type: "manual"
---

Archon Integration & Workflow
CRITICAL: This project uses Archon for knowledge management, task tracking, and project organization.

Core Archon Workflow Principles
The Golden Rule: Task-Driven Development with Archon
MANDATORY: Always complete the full Archon task cycle before any coding:

Check Current Task → Review task details and requirements
Research for Task → Search relevant documentation and examples
Implement the Task → Write code based on research
Update Task Status → Move task from "todo" → "doing" → "review"
Get Next Task → Check for next priority task
Repeat Cycle
Task Management Rules:

Update all actions to Archon
Move tasks from "todo" → "doing" → "review" (not directly to complete)
Maintain task descriptions and add implementation notes
DO NOT MAKE ASSUMPTIONS - check project documentation for questions